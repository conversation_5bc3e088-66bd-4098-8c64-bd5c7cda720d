import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiRefreshCw, <PERSON><PERSON>heck, FiX, FiHome } from 'react-icons/fi';
import {
  fetchPendingVerificationInstitutes,
  approveInstituteProfile,
  rejectInstituteProfile,
  fetchInstituteDetailsAdmin
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';
import { BasicCard, FormCard } from '../../components/ui/cards';
import { PageContainer } from '../../components/ui/layout';
import Pagination from '../../components/ui/Pagination';
import InstituteDetailsView from '../../components/admin/InstituteDetailsView';
import InstituteList from '../../components/admin/InstituteList';

const InstituteApprovals = () => {
  const dispatch = useDispatch();
  
  // Redux state
  const {
    pendingInstitutes,
    loading,
    error,
    approveLoading,
    rejectLoading,
    totalCount
  } = useSelector((state) => state.instituteProfile);

  // Local state
  const [selectedInstitute, setSelectedInstitute] = useState(null);
  const [selectedLoading, setSelectedLoading] = useState(false);
  const [currentView, setCurrentView] = useState('list'); // 'list' or 'details'
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'approve', 'reject'
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [currentPage, setCurrentPage] = useState(1); // Changed to 1-based for existing Pagination component
  const pageSize = 20;

  // Fetch pending institutes on component mount
  useEffect(() => {
    dispatch(fetchPendingVerificationInstitutes({
      skip: (currentPage - 1) * pageSize, // Convert to 0-based for API
      limit: pageSize
    }));
  }, [dispatch, currentPage, pageSize]);

  // Handle view action
  const handleView = async (institute) => {
    setSelectedInstitute(institute);
    setCurrentView('details');
    setSelectedLoading(true);

    try {
      const detailedData = await dispatch(fetchInstituteDetailsAdmin(institute.id)).unwrap();
      setSelectedInstitute(detailedData);
    } catch (error) {
      console.error('Failed to fetch detailed institute data:', error);
    } finally {
      setSelectedLoading(false);
    }
  };

  // Handle approve action
  const handleApproveAction = (institute) => {
    console.log('Opening approve modal for institute:', institute);
    setSelectedInstitute(institute);
    setModalType('approve');
    setShowModal(true);
  };

  // Handle reject action
  const handleRejectAction = (institute) => {
    console.log('Opening reject modal for institute:', institute);
    setSelectedInstitute(institute);
    setModalType('reject');
    setShowModal(true);
  };

  // Go back to list view
  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedInstitute(null);
    setSelectedLoading(false);
  };

  // Handle approve from details view
  const handleApproveFromDetails = () => {
    setModalType('approve');
    setShowModal(true);
  };

  // Handle reject from details view
  const handleRejectFromDetails = () => {
    setModalType('reject');
    setShowModal(true);
  };

  // Close modal
  const closeModal = () => {
    setShowModal(false);
    setModalType('');
    setRejectionReason('');
    setApprovalNotes('');
  };

  // Handle approve
  const handleApprove = async () => {
    if (!selectedInstitute) return;

    try {
      // Get the correct institute ID - could be user.id or institute.id
      const instituteId = selectedInstitute.user?.id || selectedInstitute.id;

      console.log('Approving institute with ID:', instituteId);
      console.log('Selected institute data:', selectedInstitute);

      const result = await dispatch(approveInstituteProfile({
        instituteId: instituteId,
        approvalData: { notes: approvalNotes }
      })).unwrap();

      console.log('Approve result:', result);

      // Show success message
      alert('Institute approved successfully!');

      closeModal();

      // Refresh the list
      dispatch(fetchPendingVerificationInstitutes({
        skip: (currentPage - 1) * pageSize,
        limit: pageSize
      }));

      // If we're in details view, go back to list
      if (currentView === 'details') {
        handleBackToList();
      }
    } catch (error) {
      console.error('Failed to approve institute:', error);
      // Show error to user
      alert(`Failed to approve institute: ${error.message || error}`);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!selectedInstitute || !rejectionReason.trim()) return;

    try {
      // Get the correct institute ID - could be user.id or institute.id
      const instituteId = selectedInstitute.user?.id || selectedInstitute.id;

      console.log('Rejecting institute with ID:', instituteId);
      console.log('Rejection reason:', rejectionReason);
      console.log('Selected institute data:', selectedInstitute);

      const result = await dispatch(rejectInstituteProfile({
        instituteId: instituteId,
        rejectionData: { reason: rejectionReason }
      })).unwrap();

      console.log('Reject result:', result);

      // Show success message
      alert('Institute rejected successfully!');

      closeModal();

      // Refresh the list
      dispatch(fetchPendingVerificationInstitutes({
        skip: (currentPage - 1) * pageSize,
        limit: pageSize
      }));

      // If we're in details view, go back to list
      if (currentView === 'details') {
        handleBackToList();
      }
    } catch (error) {
      console.error('Failed to reject institute:', error);
      // Show error to user
      alert(`Failed to reject institute: ${error.message || error}`);
    }
  };

  // Pagination
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const refreshData = () => {
    dispatch(fetchPendingVerificationInstitutes({
      skip: (currentPage - 1) * pageSize,
      limit: pageSize
    }));
  };

  // Helper function to extract institute data
  const getInstituteData = (institute) => {
    if (!institute) return { name: 'Unnamed Institute', type: 'Unknown Type', id: 'Unknown ID' };

    // Try different possible data structures
    const name = institute.institute_profile?.institute_name ||
                 institute.profile?.institute_name ||
                 institute.institute_name ||
                 institute.name ||
                 'Unnamed Institute';

    const type = institute.institute_profile?.institute_type ||
                 institute.profile?.institute_type ||
                 institute.institute_type ||
                 institute.type ||
                 'Unknown Type';

    const id = institute.user?.id || institute.id || 'Unknown ID';

    return { name, type, id };
  };

  // Render modals
  const renderModals = () => {
    if (!showModal || !selectedInstitute) return null;

    // Debug: Log the selected institute data structure
    console.log('Modal selectedInstitute:', selectedInstitute);
    console.log('Institute profile:', selectedInstitute.institute_profile);
    console.log('User data:', selectedInstitute.user);

    const instituteData = getInstituteData(selectedInstitute);
    console.log('Extracted institute data:', instituteData);

    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
          <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={closeModal} />

          <div className="relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-4">
            {modalType === 'approve' ? (
              <FormCard
                title="Approve Institute"
                icon={FiCheck}
                onSubmit={(e) => { e.preventDefault(); handleApprove(); }}
                actions={[
                  { icon: FiX, onClick: closeModal, variant: 'default' }
                ]}
              >
                <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg mb-4">
                  <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                    <FiHome className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">
                      {instituteData.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {instituteData.type}
                    </p>
                    <p className="text-xs text-gray-500">
                      ID: {instituteData.id}
                    </p>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Approval Notes (Optional)
                  </label>
                  <textarea
                    value={approvalNotes}
                    onChange={(e) => setApprovalNotes(e.target.value)}
                    rows={4}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Add any notes about the approval..."
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={approveLoading}
                    className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    {approveLoading ? (
                      <>
                        <LoadingSpinner size="sm" />
                        <span className="ml-2">Approving...</span>
                      </>
                    ) : (
                      <>
                        <FiCheck className="h-4 w-4 mr-2 inline" />
                        Approve Institute
                      </>
                    )}
                  </button>
                </div>
              </FormCard>
            ) : (
              <FormCard
                title="Reject Institute"
                icon={FiX}
                onSubmit={(e) => { e.preventDefault(); handleReject(); }}
                actions={[
                  { icon: FiX, onClick: closeModal, variant: 'default' }
                ]}
              >
                <div className="flex items-center space-x-4 p-4 bg-red-50 rounded-lg mb-4">
                  <div className="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center">
                    <FiHome className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">
                      {instituteData.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {instituteData.type}
                    </p>
                    <p className="text-xs text-gray-500">
                      ID: {instituteData.id}
                    </p>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rejection Reason <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    rows={4}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Please provide a reason for rejection..."
                    required
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={rejectLoading || !rejectionReason.trim()}
                    className="px-6 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                  >
                    {rejectLoading ? (
                      <>
                        <LoadingSpinner size="sm" />
                        <span className="ml-2">Rejecting...</span>
                      </>
                    ) : (
                      <>
                        <FiX className="h-4 w-4 mr-2 inline" />
                        Reject Institute
                      </>
                    )}
                  </button>
                </div>
              </FormCard>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render details view
  if (currentView === 'details') {
    return (
      <PageContainer>
        <InstituteDetailsView
          institute={selectedInstitute}
          loading={selectedLoading}
          onBack={handleBackToList}
          onApprove={handleApproveFromDetails}
          onReject={handleRejectFromDetails}
          approveLoading={approveLoading}
          rejectLoading={rejectLoading}
        />
        {renderModals()}
      </PageContainer>
    );
  }

  // Render list view
  return (
    <PageContainer>
      <BasicCard
        title="Institute Approvals"
        subtitle="Review and approve institute profile submissions"
        actions={[
          {
            icon: FiRefreshCw,
            onClick: refreshData,
            tooltip: "Refresh",
            variant: "default"
          }
        ]}
        loading={loading}
        error={error}
      >
        <InstituteList
          institutes={pendingInstitutes}
          onView={handleView}
          onApprove={handleApproveAction}
          onReject={handleRejectAction}
          approveLoading={approveLoading}
          rejectLoading={rejectLoading}
          emptyMessage="No pending approvals. All institute profiles have been reviewed."
        />

        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              totalItems={totalCount}
              itemsPerPage={pageSize}
            />
          </div>
        )}
      </BasicCard>

      {renderModals()}
    </PageContainer>
  );
};

export default InstituteApprovals;
