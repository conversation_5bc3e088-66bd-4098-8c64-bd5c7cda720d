import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import { ListCard } from '../ui/cards';

/**
 * InstituteList - Displays a list of institutes with actions
 * Replaces complex table structure with simpler card-based list
 */
const InstituteList = ({
  institutes = [],
  onView,
  onApprove,
  onReject,
  approveLoading = false,
  rejectLoading = false,
  emptyMessage = "No institutes found"
}) => {
  const renderInstituteItem = (institute) => {
    const actions = [
      {
        icon: FiEye,
        onClick: () => onView(institute),
        tooltip: "View Details",
        variant: "default"
      },
      {
        icon: FiCheck,
        onClick: () => {
          console.log('InstituteList - Approve clicked for:', institute);
          onApprove(institute);
        },
        tooltip: "Approve",
        variant: "success",
        disabled: approveLoading
      },
      {
        icon: FiX,
        onClick: () => {
          console.log('InstituteList - Reject clicked for:', institute);
          onReject(institute);
        },
        tooltip: "Reject",
        variant: "danger",
        disabled: rejectLoading
      }
    ];

    return (
      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div className="flex items-center space-x-4 min-w-0 flex-1">
          {/* Institute Avatar */}
          <div className="flex-shrink-0">
            {institute.user?.profile_picture || institute.profile_picture ? (
              <img
                src={institute.user?.profile_picture || institute.profile_picture}
                alt={institute.institute_profile?.institute_name || institute.institute_name}
                className="h-10 w-10 rounded-full object-cover"
              />
            ) : (
              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                <FiHome className="h-5 w-5 text-gray-500" />
              </div>
            )}
          </div>

          {/* Institute Info */}
          <div className="min-w-0 flex-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {institute.institute_profile?.institute_name || institute.institute_name || 'Unnamed Institute'}
            </h4>
            <p className="text-sm text-gray-600 truncate">
              {institute.institute_profile?.institute_type || institute.institute_type || 'Unknown Type'} • {institute.institute_profile?.city || institute.city}, {institute.institute_profile?.state || institute.state}
            </p>
          </div>

          {/* Status Badge */}
          <div className="flex-shrink-0">
            <span className={`
              inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${(institute.institute_profile?.verification_status || institute.verification_status) === 'approved'
                ? 'bg-green-100 text-green-800'
                : (institute.institute_profile?.verification_status || institute.verification_status) === 'rejected'
                ? 'bg-red-100 text-red-800'
                : 'bg-yellow-100 text-yellow-800'
              }
            `}>
              {institute.institute_profile?.verification_status || institute.verification_status || 'pending'}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          {actions.map((action, index) => {
            const ActionIcon = action.icon;
            return (
              <button
                key={index}
                onClick={action.onClick}
                disabled={action.disabled}
                className={`
                  p-2 rounded-md transition-colors duration-200
                  ${action.variant === 'success' 
                    ? 'text-green-600 hover:text-green-900 hover:bg-green-50' 
                    : action.variant === 'danger'
                    ? 'text-red-600 hover:text-red-900 hover:bg-red-50'
                    : 'text-blue-600 hover:text-blue-900 hover:bg-blue-50'
                  }
                  ${action.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                title={action.tooltip}
              >
                <ActionIcon className="h-4 w-4" />
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <ListCard
      items={institutes}
      renderItem={renderInstituteItem}
      emptyMessage={emptyMessage}
      className="space-y-0"
      contentClassName="space-y-3"
    />
  );
};

export default InstituteList;
