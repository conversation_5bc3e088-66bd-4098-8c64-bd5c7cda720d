import React from 'react';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiCalendar,
  FiFileText,
  FiAlertCircle,
  FiArrowLeft,
  FiCheck,
  FiX
} from 'react-icons/fi';
import { LoadingSpinner } from '../ui';

const InstituteDetailsView = ({
  institute,
  loading,
  onBack,
  onApprove,
  onReject,
  approveLoading,
  rejectLoading
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
        <span className="ml-3 text-gray-600">Loading institute details...</span>
      </div>
    );
  }

  if (!institute) {
    return (
      <div className="text-center py-12">
        <FiAlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Institute Selected</h3>
        <p className="text-gray-500">Please select an institute to view details.</p>
      </div>
    );
  }

  // Extract data from the correct structure
  const user = institute.user || {};
  const profile = institute.institute_profile || institute.profile || {};
  const documents = profile.documents || [];

  return (
    <div className="bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <FiArrowLeft className="h-6 w-6" />
            </button>
            <h2 className="text-2xl font-bold text-gray-900">Institute Details</h2>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => onApprove(institute)}
              disabled={approveLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {approveLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Approving...</span>
                </>
              ) : (
                <>
                  <FiCheck className="h-4 w-4 mr-2 inline" />
                  Approve Institute
                </>
              )}
            </button>
            <button
              onClick={() => onReject(institute)}
              disabled={rejectLoading}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {rejectLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Rejecting...</span>
                </>
              ) : (
                <>
                  <FiX className="h-4 w-4 mr-2 inline" />
                  Reject Institute
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {/* Institute Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 rounded-lg bg-white bg-opacity-20 flex items-center justify-center">
              <FiHome className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">
                {profile.institute_name || institute.institute_name || 'Unnamed Institute'}
              </h1>
              <p className="text-blue-100 text-lg">
                {profile.institute_type || institute.institute_type || 'Unknown Type'}
              </p>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  profile.verification_status === 'approved'
                    ? 'bg-green-500 text-white'
                    : profile.verification_status === 'rejected'
                    ? 'bg-red-500 text-white'
                    : 'bg-yellow-500 text-white'
                }`}>
                  {profile.verification_status || 'pending'}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  profile.is_verified
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-500 text-white'
                }`}>
                  {profile.is_verified ? 'Verified' : 'Not Verified'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Contact Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FiMail className="h-5 w-5 mr-2 text-blue-600" />
              Contact Information
            </h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="text-gray-900">{user.email || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Mobile</label>
                <p className="text-gray-900">{user.mobile || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Username</label>
                <p className="text-gray-900">{user.username || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Website</label>
                <p className="text-gray-900">
                  {profile.website ? (
                    <a
                      href={profile.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {profile.website}
                    </a>
                  ) : (
                    'Not provided'
                  )}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email Verified</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_email_verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_email_verified ? 'Verified' : 'Not Verified'}
                </span>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Mobile Verified</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_mobile_verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_mobile_verified ? 'Verified' : 'Not Verified'}
                </span>
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FiMapPin className="h-5 w-5 mr-2 text-blue-600" />
              Location
            </h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p className="text-gray-900">{profile.address || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">City</label>
                <p className="text-gray-900">{profile.city || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">State</label>
                <p className="text-gray-900">{profile.state || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Country</label>
                <p className="text-gray-900">{user.country || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Postal Code</label>
                <p className="text-gray-900">{profile.postal_code || 'Not provided'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        {profile.description && (
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
              Description
            </h3>
            <p className="text-gray-700 leading-relaxed">{profile.description}</p>
          </div>
        )}

        {/* Institute Details */}
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FiHome className="h-5 w-5 mr-2 text-blue-600" />
            Institute Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Established Year</label>
              <p className="text-gray-900">{profile.established_year || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Accreditation</label>
              <p className="text-gray-900">{profile.accreditation || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">User Type</label>
              <p className="text-gray-900">{user.user_type || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Total Competitions</label>
              <p className="text-gray-900">{institute.total_competitions || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Total Mentors</label>
              <p className="text-gray-900">{institute.total_mentors || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Active Competitions</label>
              <p className="text-gray-900">{institute.active_competitions || 0}</p>
            </div>
          </div>
        </div>

        {/* Additional Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Verification Notes */}
          {profile.verification_notes && (
            <div className="bg-yellow-50 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiAlertCircle className="h-5 w-5 mr-2 text-yellow-600" />
                Verification Notes
              </h4>
              <p className="text-gray-700 leading-relaxed">{profile.verification_notes}</p>
            </div>
          )}

          {/* Timestamps */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FiCalendar className="h-5 w-5 mr-2 text-blue-600" />
              Timestamps
            </h4>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">User Created</label>
                <p className="text-gray-900">{new Date(user.created_at).toLocaleString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Profile Created</label>
                <p className="text-gray-900">{new Date(profile.created_at).toLocaleString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Profile Updated</label>
                <p className="text-gray-900">{new Date(profile.updated_at).toLocaleString()}</p>
              </div>
              {profile.verified_at && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Verified At</label>
                  <p className="text-gray-900">{new Date(profile.verified_at).toLocaleString()}</p>
                </div>
              )}
            </div>
          </div>

          {/* System Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
              System Information
            </h4>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">User ID</label>
                <p className="text-gray-900 font-mono text-sm">{user.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Profile ID</label>
                <p className="text-gray-900 font-mono text-sm">{profile.id}</p>
              </div>
            </div>
          </div>

          {/* Documents */}
          {documents.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-6 lg:col-span-2">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiFileText className="h-5 w-5 mr-2 text-blue-600" />
                Documents ({documents.length})
              </h4>
              <div className="space-y-3">
                {documents.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div className="flex-1">
                      <h5 className="text-sm font-medium text-gray-900">{doc.document_name}</h5>
                      <p className="text-xs text-gray-500">
                        Type: {doc.document_type} • Created: {new Date(doc.created_at).toLocaleDateString()}
                      </p>
                      {doc.description && (
                        <p className="text-xs text-gray-600 mt-1">{doc.description}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        doc.verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {doc.verified ? 'Verified' : 'Pending'}
                      </span>
                      {doc.document_url && (
                        <a
                          href={doc.document_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          View
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Social Media Links */}
          {(profile.linkedin_url || profile.facebook_url || profile.twitter_url) && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FiGlobe className="h-5 w-5 mr-2 text-blue-600" />
                Social Media
              </h4>
              <div className="space-y-3">
                {profile.linkedin_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">LinkedIn</label>
                    <a
                      href={profile.linkedin_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 block"
                    >
                      {profile.linkedin_url}
                    </a>
                  </div>
                )}
                {profile.facebook_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Facebook</label>
                    <a
                      href={profile.facebook_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 block"
                    >
                      {profile.facebook_url}
                    </a>
                  </div>
                )}
                {profile.twitter_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Twitter</label>
                    <a
                      href={profile.twitter_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 block"
                    >
                      {profile.twitter_url}
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InstituteDetailsView;
